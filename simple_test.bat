@echo off
echo Simple Build Test - Window Stays Open
echo =====================================

echo 1. Testing Python...
python --version
echo.

echo 2. Testing pip...
python -m pip --version
echo.

echo 3. Upgrading PyInstaller...
python -m pip install --upgrade pyinstaller
echo.

echo 4. Building EXE...
python -m PyInstaller --onefile --windowed --name=MD5_Tool md5_modifier_gui.py
echo.

echo 5. Checking result...
if exist "dist\MD5_Tool.exe" (
    echo SUCCESS: EXE file created!
    if not exist "Portable" mkdir "Portable"
    copy "dist\MD5_Tool.exe" "Portable\"
    echo EXE copied to Portable folder
) else (
    echo FAILED: No EXE file found
)

echo.
echo Done! Window will stay open.
echo Press any key to close...
pause
