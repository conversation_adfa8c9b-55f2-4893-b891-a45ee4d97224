MD5 Hash Modifier - Build Instructions
=====================================

PROBLEM: Chinese characters showing as garbled text in batch files
SOLUTION: Use the English-only batch files provided

QUICK START:
1. Double-click "quick_build.bat" 
2. Wait for completion
3. Find your EXE in the "Portable" folder

DETAILED BUILD:
1. Double-click "build.bat"
2. Follow the on-screen instructions
3. Your EXE will be in the "Portable" folder

MANUAL BUILD (if batch files don't work):
1. Open Command Prompt
2. Type: pip install pyinstaller
3. Type: pyinstaller --onefile --windowed --name=MD5_Tool md5_modifier_gui.py
4. Find your EXE in the "dist" folder

REQUIREMENTS:
- Python 3.6 or higher
- Internet connection (for downloading PyInstaller)

TROUBLESHOOTING:
- If "python" command not found: Install Python from python.org
- If build fails: Try running Command Prompt as Administrator
- If antivirus blocks: Temporarily disable antivirus during build

FILES CREATED:
- MD5_Tool.exe (or MD5_Hash_Modifier.exe)
- Portable folder with all necessary files

The EXE file will work on any Windows computer without Python installed.

For more help, see BUILD_GUIDE.md
