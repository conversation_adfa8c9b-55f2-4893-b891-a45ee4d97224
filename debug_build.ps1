# MD5 Hash Modifier - Debug Build Script
# This PowerShell script provides better error reporting

Write-Host "MD5 Hash Modifier - Debug Build" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green
Write-Host ""

# Function to run command and show detailed output
function Run-Command {
    param($Command, $Description)
    Write-Host "$Description..." -ForegroundColor Cyan
    Write-Host "Command: $Command" -ForegroundColor Yellow
    
    try {
        $result = Invoke-Expression $Command 2>&1
        Write-Host "Output: $result" -ForegroundColor White
        Write-Host "Status: SUCCESS" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Status: FAILED" -ForegroundColor Red
        return $false
    }
    Write-Host ""
}

# Test Python
Write-Host "STEP 1: Testing Python Installation" -ForegroundColor Magenta
$pythonOK = Run-Command "python --version" "Checking Python version"

if (-not $pythonOK) {
    Write-Host "Trying alternative Python command..." -ForegroundColor Yellow
    $pythonOK = Run-Command "py --version" "Checking with py command"
    if ($pythonOK) {
        $pythonCmd = "py"
    }
} else {
    $pythonCmd = "python"
}

if (-not $pythonOK) {
    Write-Host "ERROR: No working Python installation found!" -ForegroundColor Red
    Write-Host "Please install Python from https://www.python.org/downloads/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit
}

Write-Host ""

# Test pip
Write-Host "STEP 2: Testing pip" -ForegroundColor Magenta
$pipOK = Run-Command "$pythonCmd -m pip --version" "Checking pip"

if (-not $pipOK) {
    Write-Host "ERROR: pip is not working!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit
}

Write-Host ""

# Check files
Write-Host "STEP 3: Checking Required Files" -ForegroundColor Magenta
if (Test-Path "md5_modifier_gui.py") {
    Write-Host "✓ md5_modifier_gui.py found" -ForegroundColor Green
} else {
    Write-Host "✗ md5_modifier_gui.py NOT found" -ForegroundColor Red
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
    Write-Host "Python files in directory:" -ForegroundColor Yellow
    Get-ChildItem *.py | ForEach-Object { Write-Host "  - $($_.Name)" }
    Read-Host "Press Enter to exit"
    exit
}

if (Test-Path "md5_modifier.py") {
    Write-Host "✓ md5_modifier.py found" -ForegroundColor Green
} else {
    Write-Host "✗ md5_modifier.py NOT found (required dependency)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit
}

Write-Host ""

# Test imports
Write-Host "STEP 4: Testing Python Imports" -ForegroundColor Magenta
$tkinterOK = Run-Command "$pythonCmd -c `"import tkinter; print('tkinter OK')`"" "Testing tkinter import"
$moduleOK = Run-Command "$pythonCmd -c `"import md5_modifier; print('md5_modifier OK')`"" "Testing md5_modifier import"

if (-not $tkinterOK -or -not $moduleOK) {
    Write-Host "ERROR: Required modules cannot be imported!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit
}

Write-Host ""

# Install/Update PyInstaller
Write-Host "STEP 5: Installing/Updating PyInstaller" -ForegroundColor Magenta
Run-Command "$pythonCmd -c `"try: import PyInstaller; print(f'Current PyInstaller: {PyInstaller.__version__}'); except: print('PyInstaller not installed')`"" "Checking current PyInstaller"

$installOK = Run-Command "$pythonCmd -m pip install --upgrade pyinstaller" "Installing/updating PyInstaller"

if (-not $installOK) {
    Write-Host "ERROR: Failed to install PyInstaller!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit
}

Write-Host ""

# Test PyInstaller
Write-Host "STEP 6: Testing PyInstaller" -ForegroundColor Magenta
$pyinstallerOK = Run-Command "$pythonCmd -m PyInstaller --version" "Testing PyInstaller command"

if (-not $pyinstallerOK) {
    Write-Host "ERROR: PyInstaller is not working!" -ForegroundColor Red
    Write-Host "This is likely due to Python 3.14 compatibility issues." -ForegroundColor Yellow
    Write-Host "Recommendation: Use Python 3.11 or 3.12" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit
}

Write-Host ""

# Clean old files
Write-Host "STEP 7: Cleaning Old Build Files" -ForegroundColor Magenta
if (Test-Path "build") { Remove-Item "build" -Recurse -Force; Write-Host "Removed build directory" }
if (Test-Path "dist") { Remove-Item "dist" -Recurse -Force; Write-Host "Removed dist directory" }
Get-ChildItem *.spec | Remove-Item -Force; Write-Host "Removed spec files"

Write-Host ""

# Build EXE
Write-Host "STEP 8: Building EXE File" -ForegroundColor Magenta
Write-Host "This may take a few minutes..." -ForegroundColor Yellow

$buildCommand = "$pythonCmd -m PyInstaller --onefile --windowed --name=MD5_Tool md5_modifier_gui.py"
Write-Host "Build command: $buildCommand" -ForegroundColor Yellow

try {
    $buildOutput = Invoke-Expression $buildCommand 2>&1
    Write-Host "Build output:" -ForegroundColor Cyan
    Write-Host $buildOutput -ForegroundColor White
    
    if (Test-Path "dist\MD5_Tool.exe") {
        Write-Host ""
        Write-Host "SUCCESS: EXE file created!" -ForegroundColor Green
        
        # Create portable package
        if (-not (Test-Path "Portable")) { New-Item -ItemType Directory -Name "Portable" }
        Copy-Item "dist\MD5_Tool.exe" "Portable\"
        if (Test-Path "README.md") { Copy-Item "README.md" "Portable\" }
        
        Write-Host "Portable package created in 'Portable' folder" -ForegroundColor Green
        Write-Host ""
        Write-Host "================================" -ForegroundColor Green
        Write-Host "BUILD COMPLETED SUCCESSFULLY!" -ForegroundColor Green
        Write-Host "================================" -ForegroundColor Green
        Write-Host ""
        Write-Host "Your files:" -ForegroundColor Cyan
        Get-ChildItem "Portable" | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor White }
        
    } else {
        Write-Host ""
        Write-Host "ERROR: Build completed but no EXE file found!" -ForegroundColor Red
        Write-Host "Check the build output above for errors." -ForegroundColor Yellow
    }
    
} catch {
    Write-Host ""
    Write-Host "ERROR: Build failed!" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Debug session complete. Press Enter to exit..." -ForegroundColor Yellow
Read-Host
