@echo off
title Simple Solution - No Compiler Required
echo Simple Solution - No Compiler Required
echo =======================================

echo PROBLEM IDENTIFIED:
echo - Python 3.14 compatibility issues
echo - Missing Microsoft Visual C++ compiler
echo - Complex dependencies failing to install
echo.

echo SOLUTION: Use simpler approach without complex dependencies
echo.

echo Step 1: Try installing a simpler version of auto-py-to-exe...
python -m pip install auto-py-to-exe==2.10.1 --no-deps --force-reinstall

if %errorlevel% neq 0 (
    echo [ERROR] Simple auto-py-to-exe installation failed
    goto :try_alternative
)

echo [OK] Simpler version installed
echo.

echo Step 2: Install only essential dependencies...
python -m pip install bottle==0.12.25
python -m pip install future
python -m pip install whichcraft

echo.
echo Step 3: Testing auto-py-to-exe...
python -c "import auto_py_to_exe; print('auto-py-to-exe imported successfully')"

if %errorlevel% equ 0 (
    echo [OK] auto-py-to-exe is working
    echo.
    echo Starting auto-py-to-exe...
    python -m auto_py_to_exe
    goto :end
)

:try_alternative
echo.
echo ================================
echo ALTERNATIVE APPROACH
echo ================================
echo.
echo Since complex tools are failing, let's try a direct approach:
echo.

echo Step 1: Create a simple standalone script...
echo Creating standalone version...

(
echo import sys
echo import os
echo import tkinter as tk
echo from tkinter import ttk, filedialog, messagebox
echo import hashlib
echo import random
echo import shutil
echo.
echo # Embedded MD5 modifier code
echo class MD5Modifier:
echo     def calculate_md5^(self, file_path^):
echo         hash_md5 = hashlib.md5^(^)
echo         with open^(file_path, "rb"^) as f:
echo             for chunk in iter^(lambda: f.read^(4096^), b""^):
echo                 hash_md5.update^(chunk^)
echo         return hash_md5.hexdigest^(^)
echo.
echo     def append_random_bytes^(self, file_path, num_bytes=1^):
echo         with open^(file_path, "ab"^) as f:
echo             random_bytes = bytes^([random.randint^(0, 255^) for _ in range^(num_bytes^)]^)
echo             f.write^(random_bytes^)
echo.
echo # Simple GUI
echo class SimpleGUI:
echo     def __init__^(self^):
echo         self.root = tk.Tk^(^)
echo         self.root.title^("MD5 Hash Modifier"^)
echo         self.root.geometry^("500x300"^)
echo         self.modifier = MD5Modifier^(^)
echo         self.setup_ui^(^)
echo.
echo     def setup_ui^(self^):
echo         ttk.Label^(self.root, text="MD5 Hash Modifier", font=^("Arial", 14^)^).pack^(pady=10^)
echo         
echo         frame = ttk.Frame^(self.root^)
echo         frame.pack^(pady=10^)
echo         
echo         ttk.Label^(frame, text="File:"^).grid^(row=0, column=0, padx=5^)
echo         self.file_var = tk.StringVar^(^)
echo         ttk.Entry^(frame, textvariable=self.file_var, width=40^).grid^(row=0, column=1, padx=5^)
echo         ttk.Button^(frame, text="Browse", command=self.browse_file^).grid^(row=0, column=2, padx=5^)
echo         
echo         ttk.Button^(self.root, text="Modify MD5", command=self.modify_file^).pack^(pady=20^)
echo         
echo         self.result_text = tk.Text^(self.root, height=8, width=60^)
echo         self.result_text.pack^(pady=10^)
echo.
echo     def browse_file^(self^):
echo         filename = filedialog.askopenfilename^(^)
echo         if filename:
echo             self.file_var.set^(filename^)
echo.
echo     def modify_file^(self^):
echo         file_path = self.file_var.get^(^)
echo         if not file_path:
echo             messagebox.showerror^("Error", "Please select a file"^)
echo             return
echo         
echo         try:
echo             original_hash = self.modifier.calculate_md5^(file_path^)
echo             self.result_text.insert^(tk.END, f"Original MD5: {original_hash}\n"^)
echo             
echo             # Create backup
echo             backup_path = file_path + ".backup"
echo             shutil.copy2^(file_path, backup_path^)
echo             self.result_text.insert^(tk.END, f"Backup created: {backup_path}\n"^)
echo             
echo             # Modify file
echo             self.modifier.append_random_bytes^(file_path^)
echo             new_hash = self.modifier.calculate_md5^(file_path^)
echo             self.result_text.insert^(tk.END, f"New MD5: {new_hash}\n"^)
echo             self.result_text.insert^(tk.END, "MD5 modification completed!\n\n"^)
echo             
echo             messagebox.showinfo^("Success", "MD5 hash modified successfully!"^)
echo         except Exception as e:
echo             messagebox.showerror^("Error", str^(e^)^)
echo.
echo     def run^(self^):
echo         self.root.mainloop^(^)
echo.
echo if __name__ == "__main__":
echo     app = SimpleGUI^(^)
echo     app.run^(^)
) > md5_tool_standalone.py

echo [OK] Standalone script created: md5_tool_standalone.py
echo.

echo Step 2: Test the standalone script...
python md5_tool_standalone.py &

echo.
echo The standalone GUI should have opened.
echo If it works, you can use this directly without needing to create an EXE.
echo.
echo To create an EXE later when you have the right tools:
echo 1. Install Visual Studio Community (free)
echo 2. Or install Microsoft C++ Build Tools
echo 3. Then retry the EXE building process
echo.

:end
echo.
echo ================================
echo SUMMARY
echo ================================
echo.
echo Option 1: Use md5_tool_standalone.py directly (no EXE needed)
echo Option 2: Install Visual Studio and retry EXE building
echo Option 3: Use Python 3.11 instead of 3.14
echo.
echo The standalone Python script should work immediately!
echo.
echo Press any key to exit...
pause >nul
