@echo off
title Try Nuitka - Alternative to PyInstaller
echo Try Nuitka - Alternative to PyInstaller
echo ==========================================

echo Nuitka is another Python-to-EXE compiler that often works better
echo with newer Python versions like 3.14.
echo.

echo Step 1: Installing Nuitka...
python -m pip install nuitka

if %errorlevel% neq 0 (
    echo [ERROR] Failed to install Nuitka
    echo Trying alternative installation...
    python -m pip install --user nuitka
    if %errorlevel% neq 0 (
        echo [ERROR] Nuitka installation failed completely
        goto :end
    )
)

echo [OK] Nuitka installed successfully
echo.

echo Step 2: Testing Nuitka...
python -m nuitka --version
if %errorlevel% neq 0 (
    echo [ERROR] Nuitka command not working
    goto :end
)

echo.
echo Step 3: Building EXE with Nuitka...
echo This may take several minutes...
echo.

echo Cleaning old files...
if exist "md5_modifier_gui.exe" del "md5_modifier_gui.exe"
if exist "md5_modifier_gui.dist" rmdir /s /q "md5_modifier_gui.dist"
if exist "md5_modifier_gui.build" rmdir /s /q "md5_modifier_gui.build"

echo Building with Nuitka...
python -m nuitka --onefile --windows-disable-console --enable-plugin=tk-inter md5_modifier_gui.py

if %errorlevel% equ 0 (
    echo.
    echo [SUCCESS] Nuitka build completed!
    
    if exist "md5_modifier_gui.exe" (
        echo [OK] EXE file created: md5_modifier_gui.exe
        
        echo Creating Portable folder...
        if not exist "Portable" mkdir "Portable"
        copy "md5_modifier_gui.exe" "Portable\MD5_Tool.exe"
        if exist "README.md" copy "README.md" "Portable\"
        
        echo.
        echo ================================
        echo SUCCESS! Nuitka build completed!
        echo ================================
        echo Your EXE file: Portable\MD5_Tool.exe
        echo.
        echo File details:
        dir "Portable\MD5_Tool.exe"
        
    ) else (
        echo [ERROR] Build succeeded but no EXE found
        echo Looking for output files...
        dir *.exe /b
    )
) else (
    echo [ERROR] Nuitka build failed
    echo.
    echo This might be due to missing C++ compiler.
    echo Nuitka requires Microsoft Visual C++ Build Tools.
    echo.
    echo You can:
    echo 1. Install Visual Studio Community (free)
    echo 2. Install "Microsoft C++ Build Tools"
    echo 3. Try auto-py-to-exe instead
)

:end
echo.
echo Press any key to exit...
pause >nul
