@echo off
title Auto-Py-to-Exe Solution for Python 3.14
echo Auto-Py-to-Exe Solution for Python 3.14
echo ==========================================

echo This tool often works better with newer Python versions like 3.14
echo.

echo Step 1: Installing auto-py-to-exe...
python -m pip install auto-py-to-exe

if %errorlevel% neq 0 (
    echo [ERROR] Failed to install auto-py-to-exe
    echo Trying with --user flag...
    python -m pip install --user auto-py-to-exe
)

echo.
echo Step 2: Starting auto-py-to-exe...
echo.
echo INSTRUCTIONS FOR THE WEB INTERFACE:
echo ===================================
echo 1. A web browser will open automatically
echo 2. In "Script Location": Click "Browse" and select "md5_modifier_gui.py"
echo 3. In "Onefile": Select "One File" (creates single EXE)
echo 4. In "Console Window": Select "Window Based (hide the console)"
echo 5. In "Output Directory": You can leave default or choose "Portable"
echo 6. Click the big blue "CONVERT .PY TO .EXE" button
echo 7. Wait for conversion to complete
echo 8. Your EXE will be in the output folder
echo.
echo Press any key to start auto-py-to-exe...
pause >nul

echo Starting auto-py-to-exe GUI...
python -m auto_py_to_exe

echo.
echo Auto-py-to-exe has finished.
echo Check the output directory for your EXE file.
echo.
echo If successful, copy the EXE to a "Portable" folder for easy distribution.
echo.
echo Press any key to exit...
pause >nul
