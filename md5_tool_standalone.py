#!/usr/bin/env python3
"""
MD5 Hash Modifier - Standalone Version
A simple, self-contained MD5 hash modification tool
No external dependencies required beyond Python standard library
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import hashlib
import random
import shutil
from pathlib import Path


class MD5Modifier:
    """Simple MD5 modifier class"""
    
    def calculate_md5(self, file_path):
        """Calculate MD5 hash of a file"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            raise Exception(f"Error calculating MD5: {e}")
    
    def append_random_bytes(self, file_path, num_bytes=1):
        """Append random bytes to file"""
        try:
            with open(file_path, "ab") as f:
                random_bytes = bytes([random.randint(0, 255) for _ in range(num_bytes)])
                f.write(random_bytes)
        except Exception as e:
            raise Exception(f"Error modifying file: {e}")
    
    def modify_existing_bytes(self, file_path, num_modifications=1):
        """Modify existing bytes in file"""
        try:
            with open(file_path, "rb") as f:
                content = bytearray(f.read())
            
            if len(content) == 0:
                raise Exception("File is empty")
            
            positions = random.sample(range(len(content)), 
                                    min(num_modifications, len(content)))
            
            for pos in positions:
                original_byte = content[pos]
                new_byte = original_byte
                while new_byte == original_byte:
                    new_byte = random.randint(0, 255)
                content[pos] = new_byte
            
            with open(file_path, "wb") as f:
                f.write(content)
                
        except Exception as e:
            raise Exception(f"Error modifying bytes: {e}")


class MD5ModifierGUI:
    """Simple GUI for MD5 modifier"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("MD5 Hash Modifier - Standalone")
        self.root.geometry("700x500")
        self.root.resizable(True, True)
        
        self.modifier = MD5Modifier()
        self.selected_file = tk.StringVar()
        self.method_var = tk.StringVar(value="append")
        self.amount_var = tk.IntVar(value=1)
        self.backup_var = tk.BooleanVar(value=True)
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="MD5 Hash Modifier", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # File selection
        file_frame = ttk.LabelFrame(main_frame, text="File Selection", padding="10")
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        file_inner_frame = ttk.Frame(file_frame)
        file_inner_frame.pack(fill=tk.X)
        
        ttk.Label(file_inner_frame, text="File:").pack(side=tk.LEFT, padx=(0, 10))
        
        file_entry = ttk.Entry(file_inner_frame, textvariable=self.selected_file)
        file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        browse_btn = ttk.Button(file_inner_frame, text="Browse...", command=self.browse_file)
        browse_btn.pack(side=tk.RIGHT)
        
        # Settings
        settings_frame = ttk.LabelFrame(main_frame, text="Modification Settings", padding="10")
        settings_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Method selection
        method_frame = ttk.Frame(settings_frame)
        method_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(method_frame, text="Method:").pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Radiobutton(method_frame, text="Append bytes (Safe)", 
                       variable=self.method_var, value="append").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(method_frame, text="Modify existing bytes", 
                       variable=self.method_var, value="modify").pack(side=tk.LEFT)
        
        # Amount selection
        amount_frame = ttk.Frame(settings_frame)
        amount_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(amount_frame, text="Amount:").pack(side=tk.LEFT, padx=(0, 10))
        
        amount_spinbox = ttk.Spinbox(amount_frame, from_=1, to=10, width=10, 
                                   textvariable=self.amount_var)
        amount_spinbox.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(amount_frame, text="bytes").pack(side=tk.LEFT)
        
        # Backup option
        backup_check = ttk.Checkbutton(settings_frame, text="Create backup file", 
                                     variable=self.backup_var)
        backup_check.pack(anchor=tk.W)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)
        
        modify_btn = ttk.Button(button_frame, text="Modify MD5 Hash", 
                               command=self.modify_file)
        modify_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_btn = ttk.Button(button_frame, text="Clear Log", command=self.clear_log)
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        exit_btn = ttk.Button(button_frame, text="Exit", command=self.root.quit)
        exit_btn.pack(side=tk.LEFT)
        
        # Log area
        log_frame = ttk.LabelFrame(main_frame, text="Operation Log", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # Initial message
        self.log("MD5 Hash Modifier - Standalone Version")
        self.log("Select a file and click 'Modify MD5 Hash' to begin")
        self.log("Backup files will be created automatically for safety")
        
    def browse_file(self):
        """Open file browser dialog"""
        filename = filedialog.askopenfilename(
            title="Select file to modify",
            filetypes=[
                ("All files", "*.*"),
                ("Text files", "*.txt"),
                ("Image files", "*.jpg *.png *.gif *.bmp"),
                ("Document files", "*.pdf *.doc *.docx"),
            ]
        )
        if filename:
            self.selected_file.set(filename)
            self.log(f"Selected file: {os.path.basename(filename)}")
    
    def log(self, message):
        """Add message to log"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """Clear the log"""
        self.log_text.delete(1.0, tk.END)
        self.log("Log cleared")
    
    def modify_file(self):
        """Modify the selected file's MD5 hash"""
        file_path = self.selected_file.get()
        
        if not file_path:
            messagebox.showerror("Error", "Please select a file first!")
            return
        
        if not os.path.exists(file_path):
            messagebox.showerror("Error", "Selected file does not exist!")
            return
        
        try:
            method = self.method_var.get()
            amount = self.amount_var.get()
            create_backup = self.backup_var.get()
            
            self.log(f"\nStarting modification of: {os.path.basename(file_path)}")
            self.log(f"Method: {method}, Amount: {amount}")
            
            # Calculate original MD5
            self.log("Calculating original MD5...")
            original_hash = self.modifier.calculate_md5(file_path)
            self.log(f"Original MD5: {original_hash}")
            
            # Create backup if requested
            backup_path = None
            if create_backup:
                backup_path = file_path + ".backup"
                counter = 1
                while os.path.exists(backup_path):
                    backup_path = f"{file_path}.backup.{counter}"
                    counter += 1
                
                shutil.copy2(file_path, backup_path)
                self.log(f"Backup created: {os.path.basename(backup_path)}")
            
            # Modify file
            self.log(f"Modifying file using {method} method...")
            if method == "append":
                self.modifier.append_random_bytes(file_path, amount)
            elif method == "modify":
                self.modifier.modify_existing_bytes(file_path, amount)
            
            # Calculate new MD5
            self.log("Calculating new MD5...")
            new_hash = self.modifier.calculate_md5(file_path)
            self.log(f"New MD5: {new_hash}")
            
            # Check if hash changed
            if original_hash != new_hash:
                self.log("✓ MD5 hash successfully modified!")
                messagebox.showinfo("Success", 
                    f"MD5 hash modified successfully!\n\n"
                    f"Original: {original_hash}\n"
                    f"New: {new_hash}")
            else:
                self.log("⚠ Warning: MD5 hash did not change")
                messagebox.showwarning("Warning", 
                    "MD5 hash did not change. Try increasing the amount.")
            
        except Exception as e:
            error_msg = f"Error: {str(e)}"
            self.log(f"✗ {error_msg}")
            messagebox.showerror("Error", error_msg)
    
    def run(self):
        """Start the GUI"""
        self.root.mainloop()


def main():
    """Main function"""
    try:
        app = MD5ModifierGUI()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        input("Press Enter to exit...")


if __name__ == "__main__":
    main()
