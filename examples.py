#!/usr/bin/env python3
"""
MD5修改工具使用示例
演示各种使用场景和最佳实践
"""

import os
import tempfile
from md5_modifier import MD5Modifier


def create_sample_files():
    """创建示例文件用于演示"""
    files = {}
    
    # 文本文件
    text_content = """这是一个示例文本文件。
包含多行内容用于测试MD5修改功能。
Text file for MD5 modification testing.
可以安全地使用append模式修改。"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(text_content)
        files['text'] = f.name
    
    # 二进制文件（模拟）
    binary_content = bytes([i % 256 for i in range(1000)])
    
    with tempfile.NamedTemporaryFile(mode='wb', suffix='.bin', delete=False) as f:
        f.write(binary_content)
        files['binary'] = f.name
    
    # 小文件
    small_content = "小文件"
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.small', delete=False, encoding='utf-8') as f:
        f.write(small_content)
        files['small'] = f.name
    
    return files


def example_1_basic_usage():
    """示例1: 基本使用 - 追加模式"""
    print("=" * 60)
    print("示例1: 基本使用 - 追加模式（推荐）")
    print("=" * 60)
    
    files = create_sample_files()
    modifier = MD5Modifier()
    
    try:
        # 使用最安全的追加模式
        file_path = files['text']
        print(f"处理文件: {file_path}")
        
        original_hash, new_hash = modifier.modify_file(
            file_path, 
            method="append", 
            amount=1, 
            create_backup=True
        )
        
        print(f"\n结果:")
        print(f"  原始MD5: {original_hash}")
        print(f"  新的MD5: {new_hash}")
        print(f"  修改成功: {'是' if original_hash != new_hash else '否'}")
        
        # 验证文件仍可读取
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"  文件仍可正常读取: 是")
        print(f"  文件大小变化: +1 字节")
        
    finally:
        cleanup_files(files)


def example_2_modify_existing():
    """示例2: 修改现有字节"""
    print("\n" + "=" * 60)
    print("示例2: 修改现有字节（谨慎使用）")
    print("=" * 60)
    
    files = create_sample_files()
    modifier = MD5Modifier()
    
    try:
        file_path = files['binary']
        print(f"处理二进制文件: {file_path}")
        
        # 读取原始内容
        with open(file_path, 'rb') as f:
            original_content = f.read()
        
        original_hash, new_hash = modifier.modify_file(
            file_path,
            method="modify",
            amount=3,  # 修改3个字节
            create_backup=True
        )
        
        # 读取修改后内容
        with open(file_path, 'rb') as f:
            modified_content = f.read()
        
        print(f"\n结果:")
        print(f"  原始MD5: {original_hash}")
        print(f"  新的MD5: {new_hash}")
        print(f"  修改成功: {'是' if original_hash != new_hash else '否'}")
        print(f"  文件大小变化: {len(modified_content) - len(original_content)} 字节")
        print(f"  修改的字节数: 3")
        
    finally:
        cleanup_files(files)


def example_3_insert_mode():
    """示例3: 插入模式"""
    print("\n" + "=" * 60)
    print("示例3: 插入随机数据（谨慎使用）")
    print("=" * 60)
    
    files = create_sample_files()
    modifier = MD5Modifier()
    
    try:
        file_path = files['small']
        print(f"处理小文件: {file_path}")
        
        # 读取原始内容
        with open(file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        original_hash, new_hash = modifier.modify_file(
            file_path,
            method="insert",
            amount=2,  # 插入2个字节
            create_backup=True
        )
        
        # 读取修改后内容
        with open(file_path, 'rb') as f:
            modified_content = f.read()
        
        print(f"\n结果:")
        print(f"  原始MD5: {original_hash}")
        print(f"  新的MD5: {new_hash}")
        print(f"  修改成功: {'是' if original_hash != new_hash else '否'}")
        print(f"  原始文件大小: {len(original_content.encode('utf-8'))} 字节")
        print(f"  修改后文件大小: {len(modified_content)} 字节")
        print(f"  插入的字节数: 2")
        
    finally:
        cleanup_files(files)


def example_4_batch_processing():
    """示例4: 批量处理多个文件"""
    print("\n" + "=" * 60)
    print("示例4: 批量处理多个文件")
    print("=" * 60)
    
    files = create_sample_files()
    
    try:
        results = []
        for file_type, file_path in files.items():
            print(f"\n处理 {file_type} 文件: {os.path.basename(file_path)}")
            
            modifier = MD5Modifier()
            original_hash, new_hash = modifier.modify_file(
                file_path,
                method="append",
                amount=1,
                create_backup=True
            )
            
            results.append({
                'type': file_type,
                'file': os.path.basename(file_path),
                'original': original_hash,
                'new': new_hash,
                'changed': original_hash != new_hash
            })
        
        print(f"\n批量处理结果汇总:")
        print("-" * 80)
        print(f"{'文件类型':<10} {'文件名':<15} {'原始MD5':<32} {'新MD5':<32} {'已改变'}")
        print("-" * 80)
        
        for result in results:
            changed_str = "是" if result['changed'] else "否"
            print(f"{result['type']:<10} {result['file']:<15} {result['original']:<32} {result['new']:<32} {changed_str}")
        
        success_count = sum(1 for r in results if r['changed'])
        print(f"\n成功修改: {success_count}/{len(results)} 个文件")
        
    finally:
        cleanup_files(files)


def cleanup_files(files):
    """清理临时文件"""
    for file_path in files.values():
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
            # 清理备份文件
            backup_path = file_path + ".backup"
            if os.path.exists(backup_path):
                os.remove(backup_path)
        except:
            pass


def main():
    """运行所有示例"""
    print("MD5修改工具使用示例")
    print("本示例将演示工具的各种使用方法和最佳实践")
    print("\n注意: 所有示例使用临时文件，不会影响您的实际文件")
    
    try:
        example_1_basic_usage()
        example_2_modify_existing()
        example_3_insert_mode()
        example_4_batch_processing()
        
        print("\n" + "=" * 60)
        print("所有示例运行完成!")
        print("=" * 60)
        print("\n推荐使用方法:")
        print("1. 对于大多数文件，使用 append 模式（最安全）")
        print("2. 始终创建备份文件（除非确定不需要）")
        print("3. 修改重要文件前先在副本上测试")
        print("4. 修改后验证文件功能是否正常")
        
    except Exception as e:
        print(f"示例运行出错: {e}")


if __name__ == "__main__":
    main()
