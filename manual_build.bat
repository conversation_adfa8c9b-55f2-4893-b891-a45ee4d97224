@echo off
echo MD5 Hash Modifier - Manual Build Script
echo =======================================

echo Step 1: Installing PyInstaller
echo.
python -m pip install pyinstaller
if %errorlevel% neq 0 (
    echo Failed to install PyInstaller, please check network connection
    pause
    exit /b 1
)

echo.
echo Step 2: Cleaning old build files
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"

echo.
echo Step 3: Starting build process
echo Building, please wait...

REM Use PyInstaller to build
pyinstaller --onefile --windowed --name="MD5_Hash_Modifier" --clean md5_modifier_gui.py

if %errorlevel% equ 0 (
    echo.
    echo [SUCCESS] Build completed!
    echo.
    echo Generated exe file location: dist\MD5_Hash_Modifier.exe
    echo.

    REM Create portable directory
    if not exist "MD5_Modifier_Portable" mkdir "MD5_Modifier_Portable"

    REM Copy exe file
    if exist "dist\MD5_Hash_Modifier.exe" (
        copy "dist\MD5_Hash_Modifier.exe" "MD5_Modifier_Portable\"
        echo [OK] Portable directory created
    )

    REM Copy documentation files
    if exist "README.md" copy "README.md" "MD5_Modifier_Portable\"
    if exist "INSTALL.md" copy "INSTALL.md" "MD5_Modifier_Portable\"

    echo.
    echo [SUCCESS] Build process completed!
    echo You can find the executable file in "MD5_Modifier_Portable" directory

) else (
    echo.
    echo [ERROR] Build failed
    echo Please check error messages and try again
)

echo.
pause
