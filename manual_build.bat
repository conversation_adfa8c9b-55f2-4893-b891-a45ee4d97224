@echo off
chcp 65001 >nul
echo MD5修改工具 - 手动打包脚本
echo ==============================

echo 步骤1: 安装PyInstaller
echo.
python -m pip install pyinstaller
if %errorlevel% neq 0 (
    echo 安装PyInstaller失败，请检查网络连接
    pause
    exit /b 1
)

echo.
echo 步骤2: 清理旧的构建文件
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"

echo.
echo 步骤3: 开始打包
echo 正在打包，请稍候...

REM 使用PyInstaller打包
pyinstaller --onefile --windowed --name="MD5修改工具" --clean md5_modifier_gui.py

if %errorlevel% equ 0 (
    echo.
    echo ✓ 打包成功!
    echo.
    echo 生成的exe文件位置: dist\MD5修改工具.exe
    echo.
    
    REM 创建便携版目录
    if not exist "MD5修改工具_便携版" mkdir "MD5修改工具_便携版"
    
    REM 复制exe文件
    if exist "dist\MD5修改工具.exe" (
        copy "dist\MD5修改工具.exe" "MD5修改工具_便携版\"
        echo ✓ 已创建便携版目录
    )
    
    REM 复制说明文件
    if exist "README.md" copy "README.md" "MD5修改工具_便携版\"
    if exist "INSTALL.md" copy "INSTALL.md" "MD5修改工具_便携版\"
    
    echo.
    echo 🎉 打包完成!
    echo 您可以在 "MD5修改工具_便携版" 目录中找到可执行文件
    
) else (
    echo.
    echo ❌ 打包失败
    echo 请检查错误信息并重试
)

echo.
pause
