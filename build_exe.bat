@echo off
chcp 65001 >nul
echo MD5修改工具 - 自动打包脚本
echo ================================

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请确保Python已安装并添加到PATH环境变量中
    echo 您可以从 https://www.python.org/downloads/ 下载并安装Python
    pause
    exit /b 1
)

echo ✓ Python环境检查通过

REM 检查必要文件
if not exist "md5_modifier.py" (
    echo 错误: 未找到md5_modifier.py文件
    pause
    exit /b 1
)

if not exist "md5_modifier_gui.py" (
    echo 错误: 未找到md5_modifier_gui.py文件
    pause
    exit /b 1
)

echo ✓ 必要文件检查通过

REM 运行打包脚本
echo.
echo 开始自动打包...
python build_exe.py

REM 检查打包结果
if %errorlevel% equ 0 (
    echo.
    echo ================================
    echo 🎉 打包成功完成!
    echo ================================
    echo.
    echo 生成的文件位置:
    if exist "MD5修改工具_便携版" (
        echo   便携版目录: MD5修改工具_便携版\
        dir "MD5修改工具_便携版" /b
    )
    echo.
    echo 您现在可以:
    echo 1. 直接运行便携版目录中的exe文件
    echo 2. 将整个便携版目录复制到其他电脑使用
    echo 3. 分享给其他用户，无需安装Python环境
) else (
    echo.
    echo ❌ 打包失败，请检查错误信息
)

echo.
pause
