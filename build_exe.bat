@echo off
echo MD5 Hash Modifier - Auto Build Script
echo ====================================

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python not found. Please install Python and add it to PATH.
    echo You can download Python from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [OK] Python environment check passed

REM Check required files
if not exist "md5_modifier.py" (
    echo Error: md5_modifier.py file not found
    pause
    exit /b 1
)

if not exist "md5_modifier_gui.py" (
    echo Error: md5_modifier_gui.py file not found
    pause
    exit /b 1
)

echo [OK] Required files check passed

REM Run build script
echo.
echo Starting auto build process...
python build_exe.py

REM Check build result
if %errorlevel% equ 0 (
    echo.
    echo ====================================
    echo [SUCCESS] Build completed successfully!
    echo ====================================
    echo.
    echo Generated files location:
    if exist "MD5_Modifier_Portable" (
        echo   Portable directory: MD5_Modifier_Portable\
        dir "MD5_Modifier_Portable" /b
    )
    echo.
    echo You can now:
    echo 1. Run the exe file in the portable directory
    echo 2. Copy the entire portable directory to other computers
    echo 3. Share with other users without Python installation
) else (
    echo.
    echo [ERROR] Build failed, please check error messages above
)

echo.
pause
