# MD5哈希修改工具 - 安装和使用指南

## 快速开始

### 1. 环境要求
- Python 3.6 或更高版本
- Windows、macOS 或 Linux 操作系统

### 2. 检查Python安装
打开命令提示符或终端，运行：
```bash
python --version
```
或
```bash
python3 --version
```

如果显示版本号（如 Python 3.8.5），说明Python已正确安装。

### 3. 下载工具
将以下文件下载到同一个文件夹中：
- `md5_modifier.py` - 主程序
- `md5_modifier.bat` - Windows批处理文件（可选）
- `README.md` - 详细说明文档

### 4. 基本使用

#### Windows用户
```cmd
# 使用批处理文件（推荐）
md5_modifier.bat your_file.txt

# 或直接使用Python
python md5_modifier.py your_file.txt
```

#### macOS/Linux用户
```bash
# 直接使用Python
python3 md5_modifier.py your_file.txt

# 或设置可执行权限后直接运行
chmod +x md5_modifier.py
./md5_modifier.py your_file.txt
```

## 常见使用场景

### 场景1: 修改文档文件的MD5
```bash
# 安全地修改PDF文件（推荐方法）
python md5_modifier.py document.pdf

# 修改Word文档
python md5_modifier.py report.docx --method append --amount 1
```

### 场景2: 修改图片文件的MD5
```bash
# 修改图片文件（通常不影响显示）
python md5_modifier.py photo.jpg

# 修改多个字节
python md5_modifier.py image.png --amount 3
```

### 场景3: 修改文本文件
```bash
# 修改文本文件
python md5_modifier.py data.txt

# 不创建备份
python md5_modifier.py temp.txt --no-backup
```

### 场景4: 高级修改（谨慎使用）
```bash
# 修改现有字节（可能影响文件功能）
python md5_modifier.py file.bin --method modify --amount 2

# 插入随机数据（可能影响文件结构）
python md5_modifier.py data.dat --method insert --amount 5
```

## 故障排除

### 问题1: "python不是内部或外部命令"
**解决方案:**
1. 确保Python已安装
2. 将Python添加到系统PATH环境变量
3. 尝试使用 `python3` 而不是 `python`

### 问题2: "权限被拒绝"
**解决方案:**
1. 确保对目标文件有读写权限
2. 以管理员身份运行命令提示符（Windows）
3. 使用 `sudo` 命令（macOS/Linux）

### 问题3: "文件正在使用中"
**解决方案:**
1. 关闭正在使用该文件的程序
2. 复制文件到其他位置后再修改
3. 重启计算机后重试

### 问题4: MD5没有改变
**解决方案:**
1. 增加修改量：`--amount 5`
2. 尝试不同的修改方法：`--method modify`
3. 检查文件是否为空文件

## 安全建议

### ✅ 推荐做法
- 始终创建备份文件（默认行为）
- 优先使用 `append` 模式
- 修改前测试工具功能
- 修改后验证文件是否正常工作
- 对重要文件先在副本上测试

### ❌ 避免做法
- 不要在没有备份的情况下修改重要文件
- 不要对可执行文件使用 `modify` 或 `insert` 模式
- 不要修改系统文件或程序文件
- 不要在生产环境中直接使用

## 测试工具

运行测试脚本验证工具功能：
```bash
python test_tool.py
```

运行示例脚本查看使用演示：
```bash
python examples.py
```

## 技术支持

如果遇到问题：
1. 查看 `README.md` 获取详细文档
2. 运行 `python md5_modifier.py --help` 查看帮助
3. 检查Python版本是否兼容
4. 确认文件路径和权限设置

## 更新日志

### v1.0.0
- 初始版本发布
- 支持三种修改模式：append、modify、insert
- 自动备份功能
- 完整的错误处理
- 中文界面支持
