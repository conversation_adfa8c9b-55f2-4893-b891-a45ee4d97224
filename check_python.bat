@echo off
title Python Environment Checker
echo Python Environment Checker
echo ===========================

echo Checking for Python installation...
echo.

REM Check python command
echo Testing: python --version
python --version 2>nul
if %errorlevel% equ 0 (
    echo [OK] python command works
    set PYTHON_CMD=python
    goto :check_pip
)

REM Check py command (Python Launcher)
echo Testing: py --version
py --version 2>nul
if %errorlevel% equ 0 (
    echo [OK] py command works
    set PYTHON_CMD=py
    goto :check_pip
)

REM Check python3 command
echo Testing: python3 --version
python3 --version 2>nul
if %errorlevel% equ 0 (
    echo [OK] python3 command works
    set PYTHON_CMD=python3
    goto :check_pip
)

echo [ERROR] No Python installation found!
echo.
echo SOLUTION:
echo 1. Download Python from: https://www.python.org/downloads/
echo 2. During installation, CHECK the box "Add Python to PATH"
echo 3. Restart your computer after installation
echo 4. Run this script again
echo.
goto :end

:check_pip
echo.
echo Checking pip...
%PYTHON_CMD% -m pip --version 2>nul
if %errorlevel% equ 0 (
    echo [OK] pip is available
    goto :install_pyinstaller
) else (
    echo [ERROR] pip not found
    echo Try: %PYTHON_CMD% -m ensurepip --upgrade
    goto :end
)

:install_pyinstaller
echo.
echo Installing PyInstaller...
%PYTHON_CMD% -m pip install pyinstaller
if %errorlevel% equ 0 (
    echo [OK] PyInstaller installed successfully
    goto :build_exe
) else (
    echo [ERROR] Failed to install PyInstaller
    echo Check your internet connection and try again
    goto :end
)

:build_exe
echo.
echo Building EXE file...
%PYTHON_CMD% -m PyInstaller --onefile --windowed --name=MD5_Tool md5_modifier_gui.py
if %errorlevel% equ 0 (
    echo [OK] EXE built successfully
    goto :create_portable
) else (
    echo [ERROR] Failed to build EXE
    goto :end
)

:create_portable
echo.
echo Creating portable package...
if not exist "Portable" mkdir "Portable"
if exist "dist\MD5_Tool.exe" (
    copy "dist\MD5_Tool.exe" "Portable\"
    echo [OK] EXE copied to Portable folder
)
if exist "README.md" copy "README.md" "Portable\"

echo.
echo ================================
echo SUCCESS! Build completed!
echo ================================
echo.
echo Your files are in the "Portable" folder:
if exist "Portable" dir "Portable" /b
echo.
echo You can now run MD5_Tool.exe on any Windows computer!

:end
echo.
echo Press any key to exit...
pause >nul
