DEBUG TOOLS - How to See What's Going Wrong
===========================================

PROBLEM: Batch files close too quickly to see error messages
SOLUTION: Use these debug tools that keep the window open

DEBUG TOOLS AVAILABLE:

1. debug_build.bat (Recommended)
   - Most detailed error reporting
   - Tests each step individually
   - Window stays open to read errors
   - Shows exact commands and their output

2. step_by_step_test.bat
   - Breaks down the process into clear steps
   - Tests each component before building
   - Good for finding exactly where it fails

3. simple_test.bat
   - Quick and simple test
   - Minimal output, easy to read
   - Good for basic troubleshooting

4. debug_build.ps1 (PowerShell version)
   - Often provides better error messages
   - Color-coded output for easy reading
   - More detailed error information

HOW TO USE:

Method 1: Use the main debug tool
1. Double-click "debug_build.bat"
2. Read all the output carefully
3. Window will stay open until you press a key
4. Look for any [ERROR] messages

Method 2: Use PowerShell (often better)
1. Right-click "debug_build.ps1"
2. Select "Run with PowerShell"
3. Read the colored output
4. Look for red error messages

Method 3: Use Command Prompt manually
1. Press Windows key + R
2. Type "cmd" and press Enter
3. Type: cd /d "C:\path\to\your\files"
4. Type: debug_build.bat
5. Read all output before closing

WHAT TO LOOK FOR:

Common Error Patterns:
- "ModuleNotFoundError: No module named 'imp'" = PyInstaller too old for Python 3.14
- "python is not recognized" = Python not installed or not in PATH
- "No module named 'tkinter'" = GUI components missing
- "Permission denied" = Run as Administrator
- "File not found" = Wrong directory or missing files

SOLUTIONS BASED ON ERRORS:

If you see "imp module" error:
- Your Python 3.14 is too new for PyInstaller
- Solution: Use Python 3.11 or 3.12

If you see "python not recognized":
- Python not installed properly
- Solution: Reinstall Python with "Add to PATH" checked

If you see "tkinter" errors:
- GUI components missing
- Solution: Reinstall Python with "tcl/tk and IDLE" option

If you see "Permission denied":
- Insufficient permissions
- Solution: Run as Administrator

If build succeeds but no EXE:
- Check dist folder manually
- Look for any warning messages in output

STEP-BY-STEP DEBUGGING:

1. Run debug_build.bat first
2. Read ALL output carefully
3. Note any [ERROR] messages
4. If Python 3.14 issues, try installing Python 3.11
5. If permission issues, run as Administrator
6. If still failing, try the PowerShell version

GETTING HELP:

When asking for help, include:
- Your Python version (from debug output)
- Any [ERROR] messages you see
- The exact point where it fails
- Your Windows version

The debug tools will show you exactly what's wrong so we can fix it!

QUICK COMMANDS TO TRY:

Open Command Prompt and try these one by one:
python --version
python -m pip --version
python -m pip install --upgrade pyinstaller
python -m PyInstaller --version
python -c "import tkinter; print('tkinter OK')"
python -c "import md5_modifier; print('module OK')"

Each command should work without errors for the build to succeed.
