@echo off
title Fix Python 3.14 + PyInstaller Issue
echo Fix Python 3.14 + PyInstaller Compatibility Issue
echo =================================================

echo PROBLEM IDENTIFIED:
echo - You have Python 3.14 (very new)
echo - PyInstaller 4.5.1 is too old for Python 3.14
echo - Python 3.14 removed 'imp' module that old PyInstaller uses
echo.

echo SOLUTION: Force install latest PyInstaller version
echo.

echo Step 1: Completely remove old PyInstaller...
python -m pip uninstall pyinstaller -y
python -m pip uninstall pyinstaller-hooks-contrib -y
python -m pip uninstall altgraph -y
echo [OK] Old PyInstaller removed

echo.
echo Step 2: Clear pip cache...
python -m pip cache purge
echo [OK] Cache cleared

echo.
echo Step 3: Update pip to latest version...
python -m pip install --upgrade pip
echo [OK] pip updated

echo.
echo Step 4: Install latest PyInstaller (this may take a few minutes)...
echo Trying latest stable version first...
python -m pip install pyinstaller --upgrade --force-reinstall --no-cache-dir

echo.
echo Step 5: Test PyInstaller installation...
python -c "import PyInstaller; print('PyInstaller version:', PyInstaller.__version__)"
if %errorlevel% neq 0 (
    echo [ERROR] Latest PyInstaller still doesn't work with Python 3.14
    echo.
    echo Trying development version...
    python -m pip install https://github.com/pyinstaller/pyinstaller/archive/develop.zip --force-reinstall
    
    echo Testing development version...
    python -c "import PyInstaller; print('PyInstaller dev version installed')"
    if %errorlevel% neq 0 (
        echo [ERROR] Even development version failed
        goto :alternative_solutions
    )
)

echo.
echo Step 6: Test PyInstaller command...
python -m PyInstaller --version
if %errorlevel% neq 0 (
    echo [ERROR] PyInstaller command still failing
    goto :alternative_solutions
)

echo.
echo Step 7: Build your EXE...
echo Cleaning old files...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "*.spec" del "*.spec"

echo Building MD5 Tool...
python -m PyInstaller --onefile --windowed --name=MD5_Tool md5_modifier_gui.py

if %errorlevel% equ 0 (
    echo.
    echo [SUCCESS] Build completed!
    
    if exist "dist\MD5_Tool.exe" (
        echo [OK] EXE file created successfully!
        
        if not exist "Portable" mkdir "Portable"
        copy "dist\MD5_Tool.exe" "Portable\"
        if exist "README.md" copy "README.md" "Portable\"
        
        echo.
        echo ================================
        echo SUCCESS! Your EXE is ready!
        echo ================================
        echo Location: Portable\MD5_Tool.exe
        echo File size:
        dir "Portable\MD5_Tool.exe"
        
    ) else (
        echo [ERROR] Build succeeded but no EXE found
    )
) else (
    echo [ERROR] Build still failed
    goto :alternative_solutions
)

goto :end

:alternative_solutions
echo.
echo ================================
echo ALTERNATIVE SOLUTIONS
echo ================================
echo.
echo Since PyInstaller doesn't work with Python 3.14, try these:
echo.
echo OPTION 1: Use Python 3.11 or 3.12 (RECOMMENDED)
echo 1. Download Python 3.11 from: https://www.python.org/downloads/release/python-3119/
echo 2. Install it alongside Python 3.14
echo 3. Use: py -3.11 -m pip install pyinstaller
echo 4. Use: py -3.11 -m PyInstaller --onefile --windowed md5_modifier_gui.py
echo.
echo OPTION 2: Use auto-py-to-exe (GUI tool)
echo 1. Run: python -m pip install auto-py-to-exe
echo 2. Run: python -m auto_py_to_exe
echo 3. Use the web interface to build your EXE
echo.
echo OPTION 3: Use cx_Freeze instead
echo 1. Run: python -m pip install cx_Freeze
echo 2. Create setup script and build with cx_Freeze
echo.
echo OPTION 4: Use Nuitka
echo 1. Run: python -m pip install nuitka
echo 2. Run: python -m nuitka --onefile --windows-disable-console md5_modifier_gui.py
echo.

:end
echo.
echo This window will stay open so you can read the results.
echo Press any key to close...
pause >nul
