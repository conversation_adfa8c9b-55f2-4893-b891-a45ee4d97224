@echo off
chcp 65001 >nul
echo MD5哈希修改工具
echo ================

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请确保Python已安装并添加到PATH环境变量中
    echo 您可以从 https://www.python.org/downloads/ 下载并安装Python
    pause
    exit /b 1
)

REM 检查md5_modifier.py是否存在
if not exist "md5_modifier.py" (
    echo 错误: 未找到md5_modifier.py文件
    echo 请确保该文件与此批处理文件在同一目录中
    pause
    exit /b 1
)

REM 如果没有参数，显示帮助信息
if "%~1"=="" (
    echo 使用方法:
    echo   md5_modifier.bat 文件路径 [选项]
    echo.
    echo 示例:
    echo   md5_modifier.bat test.txt
    echo   md5_modifier.bat document.pdf --method modify --amount 3
    echo.
    echo 更多选项请运行: python md5_modifier.py --help
    pause
    exit /b 0
)

REM 运行Python脚本
python md5_modifier.py %*

REM 如果出现错误，暂停以便查看错误信息
if %errorlevel% neq 0 (
    echo.
    echo 程序执行出现错误，错误代码: %errorlevel%
    pause
)
