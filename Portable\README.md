# MD5哈希修改工具

一个可以通过最小随机更改来修改文件MD5哈希值的Python命令行工具。

## 功能特性

- ✅ 计算并显示原始MD5哈希值
- ✅ 支持多种修改方法来改变MD5哈希
- ✅ 验证并显示修改后的新MD5哈希值
- ✅ 自动创建文件备份（可选）
- ✅ 完善的错误处理和权限检查
- ✅ 中文界面和提示信息

## 安装要求

- Python 3.6+
- 标准库（无需额外安装依赖）

## 使用方法

### 基本用法

```bash
# 使用默认设置（追加1个随机字节）
python md5_modifier.py test_file.txt

# 指定修改方法和数量
python md5_modifier.py test_file.txt --method modify --amount 3

# 不创建备份文件
python md5_modifier.py test_file.txt --no-backup
```

### 命令行参数

- `file_path`: 要修改的文件路径（必需）
- `--method, -m`: 修改方法，可选值：
  - `append`: 在文件末尾追加随机字节（默认，最安全）
  - `modify`: 修改文件中的现有字节
  - `insert`: 在文件中随机位置插入字节
- `--amount, -a`: 修改量（字节数或修改次数，默认为1）
- `--no-backup`: 不创建备份文件
- `--verbose, -v`: 显示详细信息

### 修改方法说明

#### 1. append（追加模式）- 推荐
- 在文件末尾追加随机字节
- 对大多数文件类型最安全
- 不会破坏文件的基本结构和功能
- 适用于文本文件、图片、文档等

#### 2. modify（修改模式）- 谨慎使用
- 随机修改文件中的现有字节
- 可能会影响文件的功能性
- 不建议用于可执行文件或重要数据文件
- 适用于需要保持文件大小不变的场景

#### 3. insert（插入模式）- 谨慎使用
- 在文件中随机位置插入随机字节
- 会改变文件大小和结构
- 可能严重影响文件功能
- 仅建议用于测试目的

## 使用示例

### 示例1：基本使用
```bash
python md5_modifier.py document.pdf
```
输出：
```
正在处理文件: document.pdf
原始MD5哈希值: d41d8cd98f00b204e9800998ecf8427e
✓ 已创建备份文件: document.pdf.backup
✓ 已在文件末尾追加 1 个随机字节
新的MD5哈希值: 098f6bcd4621d373cade4e832627b4f6
✓ MD5哈希值已成功修改
```

### 示例2：修改现有字节
```bash
python md5_modifier.py test.txt --method modify --amount 2
```

### 示例3：插入随机数据
```bash
python md5_modifier.py data.bin --method insert --amount 5 --no-backup
```

## 安全注意事项

1. **备份重要文件**: 默认会创建备份，建议保留此功能
2. **谨慎使用modify和insert模式**: 这些模式可能会破坏文件功能
3. **测试后验证**: 修改后请验证文件是否仍能正常使用
4. **权限检查**: 确保对目标文件有读写权限

## 错误处理

工具包含完善的错误处理机制：
- 文件不存在或路径无效
- 文件访问权限不足
- 磁盘空间不足
- 修改过程中的异常情况

如果修改失败且创建了备份，工具会询问是否从备份恢复原文件。

## 技术实现

- 使用Python标准库的`hashlib`模块计算MD5
- 支持大文件的分块读取（4KB块）
- 使用`random`模块生成随机数据
- 完整的异常处理和用户交互

## 许可证

本工具仅供学习和测试目的使用。请遵守相关法律法规，不要用于恶意目的。
