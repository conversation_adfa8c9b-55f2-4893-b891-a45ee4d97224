#!/usr/bin/env python3
"""
测试MD5修改工具的功能
"""

import os
import sys
from md5_modifier import MD5Modifier


def test_md5_modifier():
    """测试MD5修改工具的各种功能"""
    print("开始测试MD5修改工具...")
    
    # 创建测试文件
    test_file = "test_sample.txt"
    test_content = "这是一个测试文件，用于验证MD5修改工具的功能。\n包含一些中文和英文内容。\nTest content for MD5 modification."
    
    with open(test_file, "w", encoding="utf-8") as f:
        f.write(test_content)
    
    print(f"✓ 已创建测试文件: {test_file}")
    
    # 创建修改器实例
    modifier = MD5Modifier()
    
    try:
        # 测试1: 追加模式
        print("\n--- 测试1: 追加模式 ---")
        original_hash, new_hash = modifier.modify_file(
            test_file, method="append", amount=1, create_backup=True
        )
        print(f"原始哈希: {original_hash}")
        print(f"新的哈希: {new_hash}")
        print(f"哈希已改变: {'是' if original_hash != new_hash else '否'}")
        
        # 测试2: 修改模式
        print("\n--- 测试2: 修改模式 ---")
        modifier2 = MD5Modifier()
        original_hash2, new_hash2 = modifier2.modify_file(
            test_file, method="modify", amount=2, create_backup=False
        )
        print(f"修改前哈希: {new_hash}")
        print(f"修改后哈希: {new_hash2}")
        print(f"哈希已改变: {'是' if new_hash != new_hash2 else '否'}")
        
        # 验证文件内容
        print("\n--- 验证文件状态 ---")
        with open(test_file, "r", encoding="utf-8") as f:
            modified_content = f.read()
        
        print(f"原始文件大小: {len(test_content)} 字符")
        print(f"修改后文件大小: {len(modified_content)} 字符")
        print(f"文件大小变化: {len(modified_content) - len(test_content)} 字符")
        
        # 检查备份文件
        backup_files = [f for f in os.listdir(".") if f.startswith(test_file + ".backup")]
        print(f"创建的备份文件: {backup_files}")
        
        print("\n✓ 所有测试完成!")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        return False
    
    finally:
        # 清理测试文件
        try:
            if os.path.exists(test_file):
                os.remove(test_file)
            for backup in backup_files:
                if os.path.exists(backup):
                    os.remove(backup)
            print("✓ 已清理测试文件")
        except:
            pass
    
    return True


if __name__ == "__main__":
    success = test_md5_modifier()
    if success:
        print("\n🎉 MD5修改工具测试成功!")
    else:
        print("\n❌ MD5修改工具测试失败!")
        sys.exit(1)
