@echo off
title Auto-Py-to-Exe Builder
echo Auto-Py-to-Exe Builder
echo ======================

echo This tool provides a GUI interface for building EXE files
echo and is more compatible with newer Python versions.
echo.

echo Installing auto-py-to-exe...
python -m pip install auto-py-to-exe

if %errorlevel% equ 0 (
    echo [OK] auto-py-to-exe installed successfully
    echo.
    echo Starting auto-py-to-exe GUI...
    echo.
    echo INSTRUCTIONS:
    echo 1. A web browser will open with the auto-py-to-exe interface
    echo 2. In "Script Location", browse and select: md5_modifier_gui.py
    echo 3. Select "One File" option
    echo 4. Select "Window Based" (no console)
    echo 5. In "Output Directory", you can leave it default or choose "Portable"
    echo 6. Click "CONVERT .PY TO .EXE" button
    echo 7. Wait for the process to complete
    echo 8. Your EXE will be in the output/dist folder
    echo.
    echo Press any key to start auto-py-to-exe...
    pause >nul
    
    python -m auto_py_to_exe
    
) else (
    echo [ERROR] Failed to install auto-py-to-exe
    echo.
    echo Alternative solutions:
    echo 1. Try fix_pyinstaller.bat to upgrade PyInstaller
    echo 2. Use an older Python version (3.11 or 3.12)
    echo 3. Use online Python environments like Replit
)

echo.
echo Press any key to exit...
pause >nul
