@echo off
echo MD5 Hash Modifier - Build to EXE
echo =================================

echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found!
    echo Please install Python from: https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)
echo [OK] Python found

echo.
echo Installing PyInstaller...
python -m pip install pyinstaller
if %errorlevel% neq 0 (
    echo ERROR: Failed to install PyInstaller
    pause
    exit /b 1
)
echo [OK] PyInstaller installed

echo.
echo Cleaning old build files...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"
echo [OK] Cleanup completed

echo.
echo Building EXE file...
pyinstaller --onefile --windowed --name="MD5_Hash_Modifier" --clean md5_modifier_gui.py

if %errorlevel% equ 0 (
    echo [SUCCESS] EXE file created!
    
    echo.
    echo Creating portable package...
    if not exist "Portable" mkdir "Portable"
    
    if exist "dist\MD5_Hash_Modifier.exe" (
        copy "dist\MD5_Hash_Modifier.exe" "Portable\"
        echo [OK] EXE copied to Portable folder
    )
    
    if exist "README.md" copy "README.md" "Portable\"
    if exist "INSTALL.md" copy "INSTALL.md" "Portable\"
    
    echo.
    echo ================================
    echo BUILD COMPLETED SUCCESSFULLY!
    echo ================================
    echo.
    echo Your files are ready in the "Portable" folder:
    if exist "Portable" dir "Portable" /b
    echo.
    echo You can now:
    echo 1. Run MD5_Hash_Modifier.exe directly
    echo 2. Copy the Portable folder to any computer
    echo 3. No Python installation required on target computer
    
) else (
    echo [ERROR] Build failed!
    echo Please check the error messages above
)

echo.
echo Press any key to exit...
pause >nul
