PROBLEM SOLVED: 'pip' is not recognized
====================================

You're seeing this error because Python is not installed on your computer.

QUICK SOLUTIONS (choose one):

SOLUTION 1 - Automatic Installation (Recommended)
1. Right-click on "install_and_build.ps1"
2. Select "Run with PowerShell"
3. If prompted, click "Yes" to run as Administrator
4. Wait for automatic installation and building
5. Your EXE will be in the "Portable" folder

SOLUTION 2 - Manual Python Installation
1. Go to: https://www.python.org/downloads/
2. Download and install Python
3. IMPORTANT: Check "Add Python to PATH" during installation
4. Restart your computer
5. Run "check_python.bat"

SOLUTION 3 - Step by Step Check
1. Double-click "check_python.bat"
2. Follow the instructions it provides
3. It will guide you through the entire process

WHAT EACH FILE DOES:

check_python.bat        - Checks Python and guides you through installation
install_and_build.ps1   - Automatically installs Python and builds EXE
PYTHON_INSTALL_GUIDE.txt - Detailed Python installation instructions

AFTER PYTHON IS INSTALLED:
- You can use any of the build scripts
- The EXE file will work on computers without Python
- You only need to install Python once on your computer

NEED HELP?
- Read PYTHON_INSTALL_GUIDE.txt for detailed instructions
- Try the PowerShell script for automatic installation
- Ask someone with Python to build the EXE for you

The goal is to create MD5_Hash_Modifier.exe that works anywhere!
