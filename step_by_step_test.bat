@echo off
title Step by Step Test
echo Step by Step Test - Debugging Build Issues
echo ===========================================
echo.

echo STEP 1: Basic Python Test
echo --------------------------
echo Testing: python --version
python --version 2>&1
if %errorlevel% equ 0 (
    echo [OK] Python is working
) else (
    echo [ERROR] Python command failed
    echo Trying alternative: py --version
    py --version 2>&1
    if %errorlevel% equ 0 (
        echo [OK] py command works, will use 'py' instead of 'python'
        set PYTHON_CMD=py
    ) else (
        echo [ERROR] No working Python found
        goto :error_end
    )
)

if not defined PYTHON_CMD set PYTHON_CMD=python

echo.
echo STEP 2: Test pip
echo ----------------
echo Testing: %PYTHON_CMD% -m pip --version
%PYTHON_CMD% -m pip --version 2>&1
if %errorlevel% equ 0 (
    echo [OK] pip is working
) else (
    echo [ERROR] pip is not working
    goto :error_end
)

echo.
echo STEP 3: Check required files
echo ----------------------------
if exist "md5_modifier_gui.py" (
    echo [OK] md5_modifier_gui.py found
) else (
    echo [ERROR] md5_modifier_gui.py not found
    echo Current directory: %CD%
    echo Files in current directory:
    dir /b *.py
    goto :error_end
)

if exist "md5_modifier.py" (
    echo [OK] md5_modifier.py found
) else (
    echo [ERROR] md5_modifier.py not found (required by GUI version)
    goto :error_end
)

echo.
echo STEP 4: Test Python imports
echo ---------------------------
echo Testing tkinter import...
%PYTHON_CMD% -c "import tkinter; print('tkinter OK')" 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] tkinter import failed
    echo This is required for GUI applications
    goto :error_end
)

echo Testing our modules...
%PYTHON_CMD% -c "import md5_modifier; print('md5_modifier OK')" 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] md5_modifier import failed
    goto :error_end
)

echo.
echo STEP 5: Install/Update PyInstaller
echo ----------------------------------
echo Current PyInstaller status:
%PYTHON_CMD% -c "try: import PyInstaller; print('PyInstaller version:', PyInstaller.__version__); except: print('PyInstaller not found')" 2>&1

echo.
echo Installing/updating PyInstaller...
%PYTHON_CMD% -m pip install --upgrade pyinstaller 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install PyInstaller
    goto :error_end
)

echo.
echo STEP 6: Test PyInstaller
echo ------------------------
echo Testing PyInstaller command...
%PYTHON_CMD% -m PyInstaller --version 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] PyInstaller command failed
    echo Trying to diagnose the issue...
    %PYTHON_CMD% -c "import PyInstaller; print('Import OK'); import PyInstaller.__main__; print('Main module OK')" 2>&1
    goto :error_end
)

echo.
echo STEP 7: Simple Build Test
echo -------------------------
echo Creating a simple test script...
echo print('Hello from test script') > test_simple.py

echo Building simple test...
%PYTHON_CMD% -m PyInstaller --onefile test_simple.py 2>&1
if %errorlevel% equ 0 (
    echo [OK] Simple build successful
    del test_simple.py
    if exist "test_simple.spec" del "test_simple.spec"
) else (
    echo [ERROR] Even simple build failed
    echo This indicates a fundamental PyInstaller issue
    del test_simple.py
    goto :error_end
)

echo.
echo STEP 8: Build Main Application
echo ------------------------------
echo Building MD5 Tool...
%PYTHON_CMD% -m PyInstaller --onefile --windowed --name=MD5_Tool md5_modifier_gui.py 2>&1

if %errorlevel% equ 0 (
    echo [SUCCESS] Build completed!
    
    if exist "dist\MD5_Tool.exe" (
        echo [OK] EXE file created successfully
        echo File details:
        dir "dist\MD5_Tool.exe"
        
        echo Creating Portable folder...
        if not exist "Portable" mkdir "Portable"
        copy "dist\MD5_Tool.exe" "Portable\" >nul
        if exist "README.md" copy "README.md" "Portable\" >nul
        
        echo.
        echo ================================
        echo SUCCESS! Your EXE is ready!
        echo ================================
        echo Location: Portable\MD5_Tool.exe
        echo.
        
    ) else (
        echo [ERROR] Build reported success but no EXE found
    )
) else (
    echo [ERROR] Build failed
    goto :error_end
)

goto :success_end

:error_end
echo.
echo ================================
echo BUILD FAILED - DIAGNOSIS
echo ================================
echo.
echo Please check the error messages above.
echo Common solutions:
echo 1. Use Python 3.11 or 3.12 instead of 3.14
echo 2. Try: pip install pyinstaller==5.13.2
echo 3. Use auto-py-to-exe instead: pip install auto-py-to-exe
echo.
goto :final_end

:success_end
echo.
echo ================================
echo BUILD SUCCESSFUL!
echo ================================
echo.
echo Your MD5_Tool.exe is ready to use!
echo.

:final_end
echo This window will stay open for you to read the results.
echo Press any key to close...
pause >nul
