@echo off
title Fix PyInstaller Compatibility Issue
echo PyInstaller Compatibility Fix
echo ============================

echo Problem: PyInstaller 4.5.1 is not compatible with Python 3.14
echo Solution: Upgrade to latest PyInstaller version
echo.

echo Step 1: Uninstalling old PyInstaller...
python -m pip uninstall pyinstaller -y

echo.
echo Step 2: Installing latest PyInstaller...
python -m pip install --upgrade pyinstaller

echo.
echo Step 3: Verifying installation...
python -m PyInstaller --version
if %errorlevel% neq 0 (
    echo [ERROR] PyInstaller installation failed
    echo Trying alternative installation method...
    python -m pip install pyinstaller==6.0.0
)

echo.
echo Step 4: Building EXE with compatible PyInstaller...
python -m PyInstaller --onefile --windowed --name=MD5_Tool md5_modifier_gui.py

if %errorlevel% equ 0 (
    echo [SUCCESS] EXE built successfully!
    
    echo.
    echo Creating portable package...
    if not exist "Portable" mkdir "Portable"
    
    if exist "dist\MD5_Tool.exe" (
        copy "dist\MD5_Tool.exe" "Portable\"
        echo [OK] EXE copied to Portable folder
    )
    
    if exist "README.md" copy "README.md" "Portable\"
    
    echo.
    echo ================================
    echo BUILD COMPLETED SUCCESSFULLY!
    echo ================================
    echo.
    echo Your files are in the "Portable" folder:
    if exist "Portable" dir "Portable" /b
    
) else (
    echo [ERROR] Build still failed. Trying alternative approach...
    echo.
    echo Alternative: Using cx_Freeze instead of PyInstaller
    python -m pip install cx_Freeze
    if %errorlevel% equ 0 (
        echo Creating setup script for cx_Freeze...
        echo from cx_Freeze import setup, Executable > setup_freeze.py
        echo. >> setup_freeze.py
        echo setup( >> setup_freeze.py
        echo     name="MD5_Tool", >> setup_freeze.py
        echo     version="1.0", >> setup_freeze.py
        echo     description="MD5 Hash Modifier", >> setup_freeze.py
        echo     executables=[Executable("md5_modifier_gui.py", target_name="MD5_Tool.exe", base="Win32GUI")] >> setup_freeze.py
        echo ^) >> setup_freeze.py
        
        python setup_freeze.py build
        if exist "build" (
            echo [OK] cx_Freeze build completed
            echo Look for your EXE in the build folder
        )
    )
)

echo.
echo Press any key to exit...
pause >nul
