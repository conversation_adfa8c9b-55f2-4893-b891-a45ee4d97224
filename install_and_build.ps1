# MD5 Hash Modifier - PowerShell Build Script
# This script will install Python and build the EXE automatically

Write-Host "MD5 Hash Modifier - Auto Installer & Builder" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "This script needs to run as Administrator to install Python." -ForegroundColor Yellow
    Write-Host "Please right-click and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit
}

# Check if Python is already installed
Write-Host "Checking for Python installation..." -ForegroundColor Cyan

try {
    $pythonVersion = python --version 2>$null
    if ($pythonVersion) {
        Write-Host "Python found: $pythonVersion" -ForegroundColor Green
        $pythonCmd = "python"
    }
} catch {
    try {
        $pythonVersion = py --version 2>$null
        if ($pythonVersion) {
            Write-Host "Python found: $pythonVersion" -ForegroundColor Green
            $pythonCmd = "py"
        }
    } catch {
        Write-Host "Python not found. Installing Python..." -ForegroundColor Yellow
        
        # Download and install Python
        $pythonUrl = "https://www.python.org/ftp/python/3.11.0/python-3.11.0-amd64.exe"
        $pythonInstaller = "$env:TEMP\python-installer.exe"
        
        Write-Host "Downloading Python installer..." -ForegroundColor Cyan
        try {
            Invoke-WebRequest -Uri $pythonUrl -OutFile $pythonInstaller
            Write-Host "Installing Python (this may take a few minutes)..." -ForegroundColor Cyan
            Start-Process -FilePath $pythonInstaller -ArgumentList "/quiet", "InstallAllUsers=1", "PrependPath=1" -Wait
            Remove-Item $pythonInstaller
            
            # Refresh environment variables
            $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
            
            Write-Host "Python installation completed!" -ForegroundColor Green
            $pythonCmd = "python"
        } catch {
            Write-Host "Failed to install Python automatically." -ForegroundColor Red
            Write-Host "Please install Python manually from https://www.python.org/downloads/" -ForegroundColor Yellow
            Read-Host "Press Enter to exit"
            exit
        }
    }
}

# Install PyInstaller
Write-Host "Installing PyInstaller..." -ForegroundColor Cyan
try {
    & $pythonCmd -m pip install pyinstaller
    Write-Host "PyInstaller installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Failed to install PyInstaller." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit
}

# Clean old build files
Write-Host "Cleaning old build files..." -ForegroundColor Cyan
if (Test-Path "build") { Remove-Item "build" -Recurse -Force }
if (Test-Path "dist") { Remove-Item "dist" -Recurse -Force }
if (Test-Path "__pycache__") { Remove-Item "__pycache__" -Recurse -Force }

# Build EXE
Write-Host "Building EXE file..." -ForegroundColor Cyan
try {
    & $pythonCmd -m PyInstaller --onefile --windowed --name="MD5_Hash_Modifier" --clean md5_modifier_gui.py
    Write-Host "EXE built successfully!" -ForegroundColor Green
} catch {
    Write-Host "Failed to build EXE." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit
}

# Create portable package
Write-Host "Creating portable package..." -ForegroundColor Cyan
if (-not (Test-Path "Portable")) { New-Item -ItemType Directory -Name "Portable" }

if (Test-Path "dist\MD5_Hash_Modifier.exe") {
    Copy-Item "dist\MD5_Hash_Modifier.exe" "Portable\"
    Write-Host "EXE copied to Portable folder" -ForegroundColor Green
}

if (Test-Path "README.md") { Copy-Item "README.md" "Portable\" }
if (Test-Path "INSTALL.md") { Copy-Item "INSTALL.md" "Portable\" }

# Create usage instructions
$usageText = @"
MD5 Hash Modifier - Usage Instructions
=====================================

This is a portable MD5 hash modification tool.

How to use:
1. Double-click MD5_Hash_Modifier.exe
2. Click "Browse" to select a file
3. Choose modification method (recommend "Append bytes")
4. Set modification amount (default: 1 byte)
5. Ensure "Create backup file" is checked
6. Click "Modify MD5 Hash"

The tool will:
- Calculate original MD5 hash
- Create a backup of your file
- Modify the file to change its MD5 hash
- Show the new MD5 hash

Safety notes:
- Always keep backups of important files
- Use "Append bytes" method for safety
- Test on copies of important files first
- Verify file functionality after modification

For more information, see README.md
"@

$usageText | Out-File -FilePath "Portable\Usage_Instructions.txt" -Encoding UTF8

Write-Host ""
Write-Host "================================================" -ForegroundColor Green
Write-Host "BUILD COMPLETED SUCCESSFULLY!" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green
Write-Host ""
Write-Host "Your files are ready in the 'Portable' folder:" -ForegroundColor Cyan
Get-ChildItem "Portable" | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor White }
Write-Host ""
Write-Host "You can now:" -ForegroundColor Yellow
Write-Host "1. Run MD5_Hash_Modifier.exe directly" -ForegroundColor White
Write-Host "2. Copy the Portable folder to any Windows computer" -ForegroundColor White
Write-Host "3. Share with others (no Python installation required)" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
