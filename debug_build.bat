@echo off
title Debug Build - Will Not Auto Close
echo =====================================
echo DEBUG BUILD - Window will stay open
echo =====================================
echo.

echo Current directory: %CD%
echo Current time: %DATE% %TIME%
echo.

echo Testing Python installation...
echo Command: python --version
python --version
echo Exit code: %errorlevel%
echo.

echo Testing pip...
echo Command: python -m pip --version
python -m pip --version
echo Exit code: %errorlevel%
echo.

echo Checking if md5_modifier_gui.py exists...
if exist "md5_modifier_gui.py" (
    echo [OK] md5_modifier_gui.py found
) else (
    echo [ERROR] md5_modifier_gui.py NOT found
    echo Current directory contents:
    dir /b
    echo.
    echo Please make sure you are running this from the correct folder
    goto :end
)

echo.
echo Checking current PyInstaller version...
echo Command: python -m PyInstaller --version
python -m PyInstaller --version
echo Exit code: %errorlevel%
echo.

if %errorlevel% neq 0 (
    echo PyInstaller not working properly. Installing/upgrading...
    echo Command: python -m pip install --upgrade pyinstaller
    python -m pip install --upgrade pyinstaller
    echo Exit code: %errorlevel%
    echo.
    
    echo Testing PyInstaller again...
    python -m PyInstaller --version
    echo Exit code: %errorlevel%
)

echo.
echo Cleaning old build files...
if exist "build" (
    echo Removing build directory...
    rmdir /s /q "build"
)
if exist "dist" (
    echo Removing dist directory...
    rmdir /s /q "dist"
)
if exist "*.spec" (
    echo Removing spec files...
    del *.spec
)

echo.
echo Starting build process...
echo Command: python -m PyInstaller --onefile --windowed --name=MD5_Tool md5_modifier_gui.py
echo.
python -m PyInstaller --onefile --windowed --name=MD5_Tool md5_modifier_gui.py
echo.
echo Build exit code: %errorlevel%

if %errorlevel% equ 0 (
    echo.
    echo [SUCCESS] Build completed successfully!
    echo.
    echo Checking for output file...
    if exist "dist\MD5_Tool.exe" (
        echo [OK] EXE file found: dist\MD5_Tool.exe
        echo File size:
        dir "dist\MD5_Tool.exe"
        
        echo.
        echo Creating Portable folder...
        if not exist "Portable" mkdir "Portable"
        copy "dist\MD5_Tool.exe" "Portable\"
        if exist "README.md" copy "README.md" "Portable\"
        
        echo.
        echo [SUCCESS] All done! Your EXE is in the Portable folder.
        echo Contents of Portable folder:
        dir "Portable" /b
        
    ) else (
        echo [ERROR] EXE file was not created even though build seemed successful
        echo Contents of dist folder:
        if exist "dist" (
            dir "dist" /b
        ) else (
            echo dist folder does not exist
        )
    )
) else (
    echo.
    echo [ERROR] Build failed with exit code: %errorlevel%
    echo.
    echo This is likely due to Python 3.14 compatibility issues.
    echo Let's try some alternative approaches...
    echo.
    
    echo Trying with different PyInstaller options...
    echo Command: python -m PyInstaller --onefile --console md5_modifier_gui.py
    python -m PyInstaller --onefile --console md5_modifier_gui.py
    echo Exit code: %errorlevel%
    
    if %errorlevel% neq 0 (
        echo.
        echo Still failing. Let's check what's wrong...
        echo.
        echo Python version details:
        python -c "import sys; print('Python version:', sys.version); print('Python path:', sys.executable)"
        echo.
        echo PyInstaller details:
        python -c "try: import PyInstaller; print('PyInstaller version:', PyInstaller.__version__); print('PyInstaller path:', PyInstaller.__file__); except Exception as e: print('PyInstaller error:', e)"
        echo.
        echo Trying to import our GUI module:
        python -c "try: import md5_modifier_gui; print('GUI module imported successfully'); except Exception as e: print('GUI module error:', e)"
        echo.
        echo Trying to import tkinter:
        python -c "try: import tkinter; print('tkinter imported successfully'); except Exception as e: print('tkinter error:', e)"
    )
)

:end
echo.
echo =====================================
echo DEBUG SESSION COMPLETE
echo =====================================
echo.
echo This window will stay open so you can read the messages.
echo Press any key to close...
pause >nul
