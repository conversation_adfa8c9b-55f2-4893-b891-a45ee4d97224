Python Installation Guide for MD5 Hash Modifier
==============================================

PROBLEM: 'pip' is not recognized as internal or external command
CAUSE: Python is not installed or not added to PATH

SOLUTION - Install Python:

STEP 1: Download Python
- Go to: https://www.python.org/downloads/
- Click "Download Python 3.x.x" (latest version)
- Save the installer file

STEP 2: Install Python
- Run the downloaded installer
- IMPORTANT: Check the box "Add Python to PATH" !!!
- Click "Install Now"
- Wait for installation to complete
- Click "Close"

STEP 3: Verify Installation
- Press Windows key + R
- Type "cmd" and press Enter
- Type: python --version
- You should see: Python 3.x.x

STEP 4: Build Your EXE
- Double-click "check_python.bat"
- It will automatically install PyInstaller and build your EXE

ALTERNATIVE - If you can't install Python:

Option 1: Use Online Python
- Go to: https://replit.com or https://colab.research.google.com
- Upload your files and run the build script online

Option 2: Ask someone with Python
- Send them your files
- Ask them to run the build script
- They can send you back the EXE file

Option 3: Use Pre-built Version
- Look for pre-built EXE files online
- Or use similar tools that are already compiled

TROUBLESHOOTING:

Q: Python installs but 'python' command still not found
A: Restart your computer and try again

Q: Installation fails
A: Run the installer as Administrator (right-click -> Run as administrator)

Q: Still getting errors
A: Try using 'py' instead of 'python':
   py --version
   py -m pip install pyinstaller

Q: Antivirus blocks the installation
A: Temporarily disable antivirus during installation

VERIFICATION COMMANDS:
python --version          (should show Python version)
python -m pip --version   (should show pip version)
python -m pip list        (should show installed packages)

After Python is properly installed, run "check_python.bat" to automatically build your EXE file.

Need more help? 
- Python official documentation: https://docs.python.org/
- Python installation guide: https://docs.python.org/3/using/windows.html
