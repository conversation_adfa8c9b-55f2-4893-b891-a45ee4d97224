@echo off
title Manual PyInstaller Fix
echo Manual PyInstaller Compatibility Fix
echo ===================================

echo Your Python version is too new for the installed PyInstaller version.
echo Let's fix this step by step.
echo.

echo Current Python version:
python --version
echo.

echo Step 1: Completely remove old PyInstaller
python -m pip uninstall pyinstaller -y
python -m pip uninstall pyinstaller-hooks-contrib -y
python -m pip uninstall altgraph -y

echo.
echo Step 2: Clear pip cache
python -m pip cache purge

echo.
echo Step 3: Install latest PyInstaller (this may take a few minutes)
python -m pip install --upgrade pip
python -m pip install pyinstaller --upgrade --force-reinstall

echo.
echo Step 4: Verify PyInstaller works
python -c "import PyInstaller; print('PyInstaller version:', PyInstaller.__version__)"

if %errorlevel% equ 0 (
    echo [OK] PyInstaller is working
    echo.
    echo Step 5: Test build with simple script
    echo print('Hello World') > test_simple.py
    python -m PyInstaller --onefile test_simple.py
    
    if %errorlevel% equ 0 (
        echo [OK] Test build successful
        del test_simple.py
        if exist "test_simple.spec" del "test_simple.spec"
        
        echo.
        echo Step 6: Building your MD5 tool
        python -m PyInstaller --onefile --windowed --name=MD5_Tool md5_modifier_gui.py
        
        if %errorlevel% equ 0 (
            echo [SUCCESS] Your EXE has been built!
            
            if not exist "Portable" mkdir "Portable"
            if exist "dist\MD5_Tool.exe" (
                copy "dist\MD5_Tool.exe" "Portable\"
                echo [OK] EXE copied to Portable folder
            )
            if exist "README.md" copy "README.md" "Portable\"
            
            echo.
            echo ================================
            echo BUILD COMPLETED SUCCESSFULLY!
            echo ================================
            echo Your EXE is ready in the Portable folder!
            
        ) else (
            echo [ERROR] Build failed even with updated PyInstaller
            echo.
            echo Possible solutions:
            echo 1. Try using Python 3.11 or 3.12 instead of 3.14
            echo 2. Use auto-py-to-exe GUI tool
            echo 3. Use online Python environments
        )
    ) else (
        echo [ERROR] Even simple test failed
        echo Your Python 3.14 may be too new for current PyInstaller
        echo.
        echo Recommended: Install Python 3.11 or 3.12 for better compatibility
    )
) else (
    echo [ERROR] PyInstaller installation/import failed
    echo.
    echo This usually means Python 3.14 is not yet fully supported
    echo Recommendation: Use Python 3.11 or 3.12
)

echo.
echo Press any key to exit...
pause >nul
