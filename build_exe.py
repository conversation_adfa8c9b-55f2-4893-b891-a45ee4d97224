#!/usr/bin/env python3
"""
自动化打包脚本
用于将MD5修改工具打包成exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 6):
        print("❌ 错误: 需要Python 3.6或更高版本")
        return False
    print(f"✓ Python版本: {sys.version}")
    return True


def install_pyinstaller():
    """安装PyInstaller"""
    print("正在检查PyInstaller...")
    try:
        import PyInstaller
        print(f"✓ PyInstaller已安装: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("PyInstaller未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✓ PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ PyInstaller安装失败: {e}")
            return False


def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ["build", "dist", "__pycache__"]
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"清理目录: {dir_name}")
            shutil.rmtree(dir_name)


def build_exe():
    """构建exe文件"""
    print("\n开始构建exe文件...")
    
    # 方法1: 使用spec文件
    if os.path.exists("md5_modifier.spec"):
        print("使用spec文件构建...")
        cmd = ["pyinstaller", "md5_modifier.spec"]
    else:
        # 方法2: 直接构建
        print("直接构建...")
        cmd = [
            "pyinstaller",
            "--onefile",  # 打包成单个文件
            "--windowed",  # 无控制台窗口
            "--name=MD5_Hash_Modifier",
            "--clean",
            "md5_modifier_gui.py"
        ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            print("✓ exe文件构建成功!")
            return True
        else:
            print(f"❌ 构建失败:")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False


def create_portable_package():
    """创建便携版包"""
    print("\nCreating portable package...")

    # 创建发布目录
    release_dir = "MD5_Modifier_Portable"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)
    
    # 复制exe文件
    exe_path = None
    if os.path.exists("dist"):
        for file in os.listdir("dist"):
            if file.endswith(".exe"):
                exe_path = os.path.join("dist", file)
                break
    
    if exe_path and os.path.exists(exe_path):
        shutil.copy2(exe_path, release_dir)
        print(f"✓ 已复制exe文件到: {release_dir}")
    else:
        print("❌ 未找到exe文件")
        return False
    
    # 复制说明文件
    docs_to_copy = ["README.md", "INSTALL.md"]
    for doc in docs_to_copy:
        if os.path.exists(doc):
            shutil.copy2(doc, release_dir)
    
    # 创建使用说明
    usage_text = """MD5修改工具使用说明
==================

这是一个便携版的MD5哈希修改工具，无需安装即可使用。

使用方法:
1. 双击运行"MD5修改工具.exe"
2. 点击"浏览"按钮选择要修改的文件
3. 选择修改方法（推荐使用"追加字节"）
4. 设置修改数量（默认1个字节）
5. 确保"创建备份文件"选项已勾选
6. 点击"修改MD5哈希"按钮

注意事项:
- 工具会自动创建备份文件，请妥善保管
- 推荐使用"追加字节"方法，最安全
- 修改重要文件前请先测试
- 修改后请验证文件功能是否正常

技术支持:
如有问题请查看README.md文件获取详细说明。
"""
    
    with open(os.path.join(release_dir, "使用说明.txt"), "w", encoding="utf-8") as f:
        f.write(usage_text)
    
    print(f"✓ 便携版包创建完成: {release_dir}")
    return True


def main():
    """主函数"""
    print("MD5修改工具 - 自动化打包脚本")
    print("=" * 40)
    
    # 检查环境
    if not check_python_version():
        return False
    
    # 检查必要文件
    required_files = ["md5_modifier.py", "md5_modifier_gui.py"]
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 缺少必要文件: {file}")
            return False
    
    print("✓ 所有必要文件存在")
    
    # 安装PyInstaller
    if not install_pyinstaller():
        return False
    
    # 清理旧的构建文件
    clean_build_dirs()
    
    # 构建exe
    if not build_exe():
        return False
    
    # 创建便携版包
    if not create_portable_package():
        return False
    
    print("\n" + "=" * 40)
    print("🎉 打包完成!")
    print("=" * 40)
    print("生成的文件:")
    
    # 显示生成的文件
    if os.path.exists("dist"):
        print("exe文件目录: dist/")
        for file in os.listdir("dist"):
            print(f"  - {file}")
    
    if os.path.exists("MD5修改工具_便携版"):
        print("便携版目录: MD5修改工具_便携版/")
        for file in os.listdir("MD5修改工具_便携版"):
            print(f"  - {file}")
    
    print("\n使用建议:")
    print("1. 可以直接使用便携版目录中的exe文件")
    print("2. 将整个便携版目录复制到其他电脑使用")
    print("3. 无需安装Python环境即可运行")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 打包失败!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n用户取消操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包过程出现异常: {e}")
        sys.exit(1)
