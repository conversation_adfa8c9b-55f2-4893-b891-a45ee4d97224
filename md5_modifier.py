#!/usr/bin/env python3
"""
MD5哈希修改工具
一个可以通过最小随机更改来修改文件MD5哈希值的命令行工具
"""

import argparse
import hashlib
import os
import random
import shutil
import sys
from pathlib import Path
from typing import Optional, Tuple


class MD5Modifier:
    """MD5哈希修改器类"""
    
    def __init__(self):
        self.original_hash = None
        self.new_hash = None
    
    def calculate_md5(self, file_path: str) -> str:
        """计算文件的MD5哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except IOError as e:
            raise IOError(f"无法读取文件 {file_path}: {e}")
    
    def create_backup(self, file_path: str) -> str:
        """创建文件备份"""
        backup_path = f"{file_path}.backup"
        counter = 1
        while os.path.exists(backup_path):
            backup_path = f"{file_path}.backup.{counter}"
            counter += 1
        
        try:
            shutil.copy2(file_path, backup_path)
            print(f"✓ 已创建备份文件: {backup_path}")
            return backup_path
        except IOError as e:
            raise IOError(f"无法创建备份文件: {e}")
    
    def append_random_bytes(self, file_path: str, num_bytes: int = 1) -> None:
        """在文件末尾追加随机字节"""
        try:
            with open(file_path, "ab") as f:
                random_bytes = bytes([random.randint(0, 255) for _ in range(num_bytes)])
                f.write(random_bytes)
            print(f"✓ 已在文件末尾追加 {num_bytes} 个随机字节")
        except IOError as e:
            raise IOError(f"无法修改文件 {file_path}: {e}")
    
    def modify_existing_bytes(self, file_path: str, num_modifications: int = 1) -> None:
        """修改文件中的现有字节"""
        try:
            # 读取文件内容
            with open(file_path, "rb") as f:
                content = bytearray(f.read())
            
            if len(content) == 0:
                raise ValueError("文件为空，无法修改现有字节")
            
            # 随机选择要修改的位置
            positions = random.sample(range(len(content)), 
                                    min(num_modifications, len(content)))
            
            for pos in positions:
                # 生成与原字节不同的新字节
                original_byte = content[pos]
                new_byte = original_byte
                while new_byte == original_byte:
                    new_byte = random.randint(0, 255)
                content[pos] = new_byte
            
            # 写回文件
            with open(file_path, "wb") as f:
                f.write(content)
            
            print(f"✓ 已修改 {len(positions)} 个字节位置: {positions}")
        except IOError as e:
            raise IOError(f"无法修改文件 {file_path}: {e}")
    
    def insert_random_data(self, file_path: str, position: Optional[int] = None, 
                          num_bytes: int = 1) -> None:
        """在文件中插入随机数据"""
        try:
            with open(file_path, "rb") as f:
                content = f.read()
            
            if position is None:
                position = random.randint(0, len(content))
            else:
                position = min(position, len(content))
            
            random_bytes = bytes([random.randint(0, 255) for _ in range(num_bytes)])
            new_content = content[:position] + random_bytes + content[position:]
            
            with open(file_path, "wb") as f:
                f.write(new_content)
            
            print(f"✓ 已在位置 {position} 插入 {num_bytes} 个随机字节")
        except IOError as e:
            raise IOError(f"无法修改文件 {file_path}: {e}")
    
    def modify_file(self, file_path: str, method: str = "append", 
                   amount: int = 1, create_backup: bool = True) -> Tuple[str, str]:
        """修改文件的MD5哈希值"""
        # 验证文件存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        if not os.path.isfile(file_path):
            raise ValueError(f"路径不是文件: {file_path}")
        
        # 计算原始MD5
        print(f"正在处理文件: {file_path}")
        self.original_hash = self.calculate_md5(file_path)
        print(f"原始MD5哈希值: {self.original_hash}")
        
        # 创建备份
        backup_path = None
        if create_backup:
            backup_path = self.create_backup(file_path)
        
        try:
            # 根据方法修改文件
            if method == "append":
                self.append_random_bytes(file_path, amount)
            elif method == "modify":
                self.modify_existing_bytes(file_path, amount)
            elif method == "insert":
                self.insert_random_data(file_path, num_bytes=amount)
            else:
                raise ValueError(f"不支持的修改方法: {method}")
            
            # 计算新的MD5
            self.new_hash = self.calculate_md5(file_path)
            print(f"新的MD5哈希值: {self.new_hash}")
            
            # 验证哈希值已改变
            if self.original_hash == self.new_hash:
                print("⚠️  警告: MD5哈希值未发生变化，可能需要增加修改量")
            else:
                print("✓ MD5哈希值已成功修改")
            
            return self.original_hash, self.new_hash
            
        except Exception as e:
            # 如果修改失败且创建了备份，询问是否恢复
            if backup_path and os.path.exists(backup_path):
                print(f"修改失败: {e}")
                response = input("是否从备份恢复原文件? (y/n): ").lower()
                if response in ['y', 'yes', '是']:
                    shutil.copy2(backup_path, file_path)
                    print("✓ 已从备份恢复原文件")
            raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="MD5哈希修改工具 - 通过最小更改修改文件的MD5哈希值",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
修改方法说明:
  append  - 在文件末尾追加随机字节 (默认，对大多数文件类型安全)
  modify  - 修改文件中的现有字节 (可能影响文件功能)
  insert  - 在文件中随机位置插入字节 (可能影响文件功能)

使用示例:
  python md5_modifier.py file.txt
  python md5_modifier.py file.txt --method modify --amount 3
  python md5_modifier.py file.txt --no-backup --method append --amount 5
        """
    )
    
    parser.add_argument("file_path", help="要修改的文件路径")
    parser.add_argument("--method", "-m", choices=["append", "modify", "insert"],
                       default="append", help="修改方法 (默认: append)")
    parser.add_argument("--amount", "-a", type=int, default=1,
                       help="修改量 (字节数或修改次数，默认: 1)")
    parser.add_argument("--no-backup", action="store_true",
                       help="不创建备份文件")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="显示详细信息")
    
    args = parser.parse_args()
    
    # 创建修改器实例
    modifier = MD5Modifier()
    
    try:
        # 执行修改
        original_hash, new_hash = modifier.modify_file(
            args.file_path,
            method=args.method,
            amount=args.amount,
            create_backup=not args.no_backup
        )
        
        print("\n" + "="*50)
        print("修改完成!")
        print(f"原始哈希: {original_hash}")
        print(f"新的哈希: {new_hash}")
        print(f"哈希已改变: {'是' if original_hash != new_hash else '否'}")
        
    except KeyboardInterrupt:
        print("\n操作被用户取消")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
