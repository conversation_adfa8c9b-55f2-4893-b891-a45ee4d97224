#!/usr/bin/env python3
"""
MD5哈希修改工具 - GUI版本
带图形界面的MD5哈希修改工具，适合打包成exe文件
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
from pathlib import Path

# 导入核心功能
from md5_modifier import MD5Modifier


class MD5ModifierGUI:
    """MD5修改工具图形界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("MD5哈希修改工具 v1.0")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置图标（如果有的话）
        try:
            # 可以在这里设置应用图标
            pass
        except:
            pass
        
        self.modifier = MD5Modifier()
        self.selected_file = tk.StringVar()
        self.method_var = tk.StringVar(value="append")
        self.amount_var = tk.IntVar(value=1)
        self.backup_var = tk.BooleanVar(value=True)
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="MD5哈希修改工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Label(file_frame, text="选择文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        file_entry = ttk.Entry(file_frame, textvariable=self.selected_file, width=50)
        file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        browse_btn = ttk.Button(file_frame, text="浏览...", command=self.browse_file)
        browse_btn.grid(row=0, column=2)
        
        # 设置区域
        settings_frame = ttk.LabelFrame(main_frame, text="修改设置", padding="10")
        settings_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 修改方法
        ttk.Label(settings_frame, text="修改方法:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        method_frame = ttk.Frame(settings_frame)
        method_frame.grid(row=0, column=1, sticky=tk.W)
        
        ttk.Radiobutton(method_frame, text="追加字节 (推荐)", variable=self.method_var, 
                       value="append").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(method_frame, text="修改现有字节", variable=self.method_var, 
                       value="modify").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(method_frame, text="插入字节", variable=self.method_var, 
                       value="insert").pack(side=tk.LEFT)
        
        # 修改数量
        ttk.Label(settings_frame, text="修改数量:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        
        amount_frame = ttk.Frame(settings_frame)
        amount_frame.grid(row=1, column=1, sticky=tk.W, pady=(10, 0))
        
        amount_spinbox = ttk.Spinbox(amount_frame, from_=1, to=100, width=10, 
                                   textvariable=self.amount_var)
        amount_spinbox.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(amount_frame, text="字节").pack(side=tk.LEFT)
        
        # 备份选项
        backup_check = ttk.Checkbutton(settings_frame, text="创建备份文件", 
                                     variable=self.backup_var)
        backup_check.grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=(10, 0))
        
        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=(10, 0))
        
        self.modify_btn = ttk.Button(button_frame, text="修改MD5哈希", 
                                   command=self.modify_file_threaded, style="Accent.TButton")
        self.modify_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.clear_btn = ttk.Button(button_frame, text="清除日志", command=self.clear_log)
        self.clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.exit_btn = ttk.Button(button_frame, text="退出", command=self.root.quit)
        self.exit_btn.pack(side=tk.LEFT)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        result_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(result_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 初始化日志
        self.log("MD5哈希修改工具已启动")
        self.log("请选择要修改的文件，然后点击'修改MD5哈希'按钮")
        
    def browse_file(self):
        """浏览文件对话框"""
        filename = filedialog.askopenfilename(
            title="选择要修改MD5的文件",
            filetypes=[
                ("所有文件", "*.*"),
                ("文本文件", "*.txt"),
                ("图片文件", "*.jpg;*.png;*.gif;*.bmp"),
                ("文档文件", "*.pdf;*.doc;*.docx"),
                ("可执行文件", "*.exe;*.dll")
            ]
        )
        if filename:
            self.selected_file.set(filename)
            self.log(f"已选择文件: {filename}")
    
    def log(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)
        self.log("日志已清除")
    
    def set_status(self, message):
        """设置状态栏消息"""
        self.status_var.set(message)
        self.root.update_idletasks()
    
    def modify_file_threaded(self):
        """在新线程中修改文件（避免界面冻结）"""
        if not self.selected_file.get():
            messagebox.showerror("错误", "请先选择要修改的文件！")
            return
        
        # 在新线程中执行修改操作
        thread = threading.Thread(target=self.modify_file)
        thread.daemon = True
        thread.start()
    
    def modify_file(self):
        """修改文件MD5哈希"""
        try:
            # 禁用按钮
            self.modify_btn.config(state="disabled")
            self.set_status("正在修改文件...")
            
            file_path = self.selected_file.get()
            method = self.method_var.get()
            amount = self.amount_var.get()
            create_backup = self.backup_var.get()
            
            self.log(f"\n开始修改文件: {os.path.basename(file_path)}")
            self.log(f"修改方法: {method}")
            self.log(f"修改数量: {amount}")
            self.log(f"创建备份: {'是' if create_backup else '否'}")
            self.log("-" * 50)
            
            # 检查文件
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 计算原始MD5
            self.log("正在计算原始MD5哈希...")
            original_hash = self.modifier.calculate_md5(file_path)
            self.log(f"原始MD5: {original_hash}")
            
            # 创建备份
            if create_backup:
                self.log("正在创建备份文件...")
                backup_path = self.modifier.create_backup(file_path)
                self.log(f"备份文件: {os.path.basename(backup_path)}")
            
            # 执行修改
            self.log(f"正在使用 {method} 方法修改文件...")
            if method == "append":
                self.modifier.append_random_bytes(file_path, amount)
            elif method == "modify":
                self.modifier.modify_existing_bytes(file_path, amount)
            elif method == "insert":
                self.modifier.insert_random_data(file_path, num_bytes=amount)
            
            # 计算新MD5
            self.log("正在计算新的MD5哈希...")
            new_hash = self.modifier.calculate_md5(file_path)
            self.log(f"新的MD5: {new_hash}")
            
            # 显示结果
            if original_hash != new_hash:
                self.log("✓ MD5哈希修改成功！")
                self.set_status("修改完成")
                messagebox.showinfo("成功", "MD5哈希修改成功！\n\n" +
                                  f"原始MD5: {original_hash}\n" +
                                  f"新的MD5: {new_hash}")
            else:
                self.log("⚠ 警告: MD5哈希未发生变化")
                self.set_status("修改完成（哈希未变化）")
                messagebox.showwarning("警告", "MD5哈希未发生变化，可能需要增加修改数量")
            
        except Exception as e:
            error_msg = f"修改失败: {str(e)}"
            self.log(f"❌ {error_msg}")
            self.set_status("修改失败")
            messagebox.showerror("错误", error_msg)
        
        finally:
            # 重新启用按钮
            self.modify_btn.config(state="normal")


def main():
    """主函数"""
    # 创建主窗口
    root = tk.Tk()
    
    # 设置主题（如果支持）
    try:
        style = ttk.Style()
        # 尝试使用现代主题
        available_themes = style.theme_names()
        if 'vista' in available_themes:
            style.theme_use('vista')
        elif 'clam' in available_themes:
            style.theme_use('clam')
    except:
        pass
    
    # 创建应用
    app = MD5ModifierGUI(root)
    
    # 设置窗口关闭事件
    def on_closing():
        if messagebox.askokcancel("退出", "确定要退出MD5修改工具吗？"):
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # 运行主循环
    root.mainloop()


if __name__ == "__main__":
    main()
